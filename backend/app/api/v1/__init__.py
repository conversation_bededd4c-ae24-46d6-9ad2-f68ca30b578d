from app.api.base import BaseAPIRouter
from app.api.v1 import (
    auth,
    cache,
    chat,
    deals,
    exclusion_filters,
    forms,
    jobs,
    organizations,
    public,
    qualifier_forms,
    queue,
    rbac,
    roles,
    sharing,
    thesis,
    triggers,
    uploads,
    users,
)

protected_api_router = BaseAPIRouter()
public_api_router = BaseAPIRouter(disable_auth=True, require_org=False)
# Include system-level routers first
protected_api_router.include_router(organizations.system_router)

# Include public auth routes
public_api_router.include_router(auth.public_router)

# Include public form routes
public_api_router.include_router(forms.public_router)

# Include public submission routes
public_api_router.include_router(public.router)

# Include public file routes
from . import public_file

public_api_router.include_router(public_file.router)

# Include onboarding routes
from . import onboarding

public_api_router.include_router(onboarding.public_router)
protected_api_router.include_router(onboarding.protected_router)

# Include settings routes
from . import settings

protected_api_router.include_router(settings.router)

# Include protected auth routes
protected_api_router.include_router(auth.protected_router)

# Include queue routes
protected_api_router.include_router(queue.router)

# Include sharing routes
protected_api_router.include_router(sharing.router)

# Include trigger routes
protected_api_router.include_router(triggers.router)

# Include qualifier form routes
protected_api_router.include_router(qualifier_forms.router)

# Include exclusion filter routes
protected_api_router.include_router(exclusion_filters.router)

# Include other routers
protected_api_router.include_router(users.router)
protected_api_router.include_router(organizations.router)
protected_api_router.include_router(roles.router)
protected_api_router.include_router(forms.router)
protected_api_router.include_router(thesis.router)
protected_api_router.include_router(cache.router)
protected_api_router.include_router(rbac.router)
protected_api_router.include_router(jobs.router)
protected_api_router.include_router(deals.router)
protected_api_router.include_router(chat.router)
protected_api_router.include_router(uploads.router)
