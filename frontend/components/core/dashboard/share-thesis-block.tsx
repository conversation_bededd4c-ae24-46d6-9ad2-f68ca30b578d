"use client"

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  Share2,
  Copy,
  QrCode,
  ExternalLink,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { DashboardSummary } from '@/lib/api/dashboard-api';

interface ShareThesisBlockProps {
  dashboardData: DashboardSummary;
  className?: string;
}

export function ShareThesisBlock({
  dashboardData,
  className
}: ShareThesisBlockProps) {
  // Generate mock share URL and form title based on dashboard data
  const shareUrl = dashboardData.forms > 0 ? 'https://app.tractionx.com/share/demo-form' : '';
  const formTitle = dashboardData.forms > 0 ? 'Investment Application Form' : 'No forms available';
  const hasForm = dashboardData.forms > 0;
  const [copied, setCopied] = useState(false);

  const handleCopyToClipboard = async () => {
    if (!hasForm) return;

    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);

      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleOpenLink = () => {
    if (!hasForm) return;
    window.open(shareUrl, '_blank');
  };

  // Generate QR code URL (using a free QR code service)
  const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=${encodeURIComponent(shareUrl)}`;

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
        delay: 0.6
      }
    }
  };

  const qrVariants = {
    hidden: { opacity: 0, rotate: -10 },
    visible: {
      opacity: 1,
      rotate: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        delay: 0.8
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={className}
    >
      <Card className="relative overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950/20 dark:via-indigo-950/20 dark:to-purple-950/20" />

        <CardHeader className="relative">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold flex items-center gap-3">
                <Share2 className="h-6 w-6 text-blue-600" />
                Share Investment Form
              </CardTitle>
              <p className="text-base text-muted-foreground mt-2">
                Let startups apply directly through your form
              </p>
            </div>
            <Badge variant="secondary" className="bg-blue-100 text-blue-700 hover:bg-blue-100 text-sm px-3 py-1">
              Active
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="relative space-y-6">
          {hasForm ? (
            <>
              {/* QR Code Section */}
              <div className="flex items-center gap-4">
                <motion.div
                  variants={qrVariants}
                  className="flex-shrink-0"
                >
                  <div className="p-3 bg-white rounded-lg shadow-sm border">
                    <img
                      src={qrCodeUrl}
                      alt="QR Code for form sharing"
                      className="w-20 h-20"
                    />
                  </div>
                </motion.div>

            <div className="flex-1 min-w-0">
              <h4 className="font-semibold text-foreground mb-2 text-lg">
                {formTitle}
              </h4>
              <p className="text-base text-muted-foreground mb-4">
                Scan QR code or use the link below
              </p>

              {/* URL Display */}
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <code className="flex-1 text-sm font-mono text-muted-foreground truncate">
                  {shareUrl}
                </code>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleCopyToClipboard}
              variant="default"
              size="sm"
              className="flex-1"
              disabled={copied}
            >
              {copied ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Link
                </>
              )}
            </Button>

            <Button
              onClick={handleOpenLink}
              variant="outline"
              size="sm"
              className="flex-1"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Preview
            </Button>
          </div>
          </>
          ) : (
            <div className="text-center py-8">
              <Share2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h4 className="font-semibold text-foreground mb-2">No Forms Available</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Create your first form to start sharing with startups
              </p>
              <Button asChild size="sm">
                <a href="/forms/new">Create Form</a>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
