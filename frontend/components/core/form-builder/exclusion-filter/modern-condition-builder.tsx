"use client"

import React, { useState, useEffect } from 'react';
import { Trash2, AlertCircle, X } from 'lucide-react';
import { motion } from 'framer-motion';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

import { Question } from '@/lib/types/form';
import { ExclusionOperator } from '@/lib/types/exclusion-filter';

interface LocalCondition {
  id: string;
  question_id: string;
  operator: ExclusionOperator;
  value: any;
  isValid: boolean;
  error?: string;
}

interface ModernConditionBuilderProps {
  condition: LocalCondition;
  questions: Question[];
  index: number;
  showLogicOperator?: boolean;
  onUpdate: (updates: Partial<LocalCondition>) => void;
  onRemove: () => void;
}

// Define operators for each question type
const OPERATORS_BY_TYPE = {
  single_select: [
    { value: ExclusionOperator.EQUALS, label: 'equals', symbol: '=' },
    { value: ExclusionOperator.NOT_EQUALS, label: 'does not equal', symbol: '≠' },
  ],
  multi_select: [
    { value: ExclusionOperator.IN, label: 'is one of', symbol: '∈' },
    { value: ExclusionOperator.NOT_IN, label: 'is not one of', symbol: '∉' },
  ],
  boolean: [
    { value: ExclusionOperator.EQUALS, label: 'equals', symbol: '=' },
    { value: ExclusionOperator.NOT_EQUALS, label: 'does not equal', symbol: '≠' },
  ],
};

export function ModernConditionBuilder({
  condition,
  questions,
  index,
  showLogicOperator = false,
  onUpdate,
  onRemove,
}: ModernConditionBuilderProps) {
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);

  // Find the selected question
  useEffect(() => {
    if (condition.question_id) {
      const question = questions.find(q => 
        (q._id === condition.question_id) || (q.id === condition.question_id)
      );
      setSelectedQuestion(question || null);
    } else {
      setSelectedQuestion(null);
    }
  }, [condition.question_id, questions]);

  // Validate the condition
  useEffect(() => {
    let isValid = true;
    let error = '';

    if (!condition.question_id) {
      isValid = false;
      error = 'Please select a question';
    } else if (!selectedQuestion) {
      isValid = false;
      error = 'Selected question not found';
    } else if (!condition.operator) {
      isValid = false;
      error = 'Please select an operator';
    } else if (condition.value === '' || condition.value === null || condition.value === undefined) {
      isValid = false;
      error = 'Please select a value';
    } else {
      // Validate value based on question type and operator
      if (selectedQuestion.type === 'multi_select' && 
          (condition.operator === ExclusionOperator.IN || condition.operator === ExclusionOperator.NOT_IN)) {
        if (!Array.isArray(condition.value) || condition.value.length === 0) {
          isValid = false;
          error = 'Please select at least one option';
        }
      }
    }

    onUpdate({ isValid, error });
  }, [condition, selectedQuestion, onUpdate]);

  // Get available operators for the selected question type
  const getAvailableOperators = () => {
    if (!selectedQuestion) return [];
    return OPERATORS_BY_TYPE[selectedQuestion.type as keyof typeof OPERATORS_BY_TYPE] || [];
  };

  // Handle question selection
  const handleQuestionChange = (questionId: string) => {
    const question = questions.find(q => (q._id === questionId) || (q.id === questionId));
    
    onUpdate({
      question_id: questionId,
      operator: ExclusionOperator.EQUALS, // Reset operator
      value: '', // Reset value
      isValid: false
    });
  };

  // Handle operator change
  const handleOperatorChange = (operator: ExclusionOperator) => {
    // Reset value when operator changes
    let resetValue = '';
    
    // For multi-select IN/NOT_IN operators, initialize as empty array
    if (selectedQuestion?.type === 'multi_select' && 
        (operator === ExclusionOperator.IN || operator === ExclusionOperator.NOT_IN)) {
      resetValue = [];
    }

    onUpdate({
      operator,
      value: resetValue,
      isValid: false
    });
  };

  // Handle value change for option selection
  const handleOptionToggle = (optionValue: string) => {
    if (!selectedQuestion) return;

    if (selectedQuestion.type === 'multi_select' && 
        (condition.operator === ExclusionOperator.IN || condition.operator === ExclusionOperator.NOT_IN)) {
      // Multi-select: toggle option in array
      const currentValues = Array.isArray(condition.value) ? condition.value : [];
      let newValues;
      
      if (currentValues.includes(optionValue)) {
        newValues = currentValues.filter(v => v !== optionValue);
      } else {
        newValues = [...currentValues, optionValue];
      }
      
      onUpdate({ value: newValues });
    } else {
      // Single select or boolean: set single value
      onUpdate({ value: optionValue });
    }
  };

  // Remove option for multi-select
  const handleOptionRemove = (optionValue: string) => {
    if (Array.isArray(condition.value)) {
      const newValues = condition.value.filter(v => v !== optionValue);
      onUpdate({ value: newValues });
    }
  };

  // Get question options
  const getQuestionOptions = () => {
    if (!selectedQuestion) return [];
    
    if (selectedQuestion.type === 'boolean') {
      return [
        { label: 'Yes', value: 'true' },
        { label: 'No', value: 'false' }
      ];
    }
    
    return selectedQuestion.options || [];
  };

  // Check if option is selected
  const isOptionSelected = (optionValue: string) => {
    if (Array.isArray(condition.value)) {
      return condition.value.includes(optionValue);
    }
    return condition.value === optionValue;
  };

  // Render selected values as tags
  const renderSelectedTags = () => {
    const options = getQuestionOptions();
    
    if (Array.isArray(condition.value)) {
      // Multi-select values
      return condition.value.map(value => {
        const option = options.find(opt => opt.value === value);
        return (
          <Badge
            key={value}
            variant="secondary"
            className="px-3 py-1 text-sm font-medium"
          >
            {option?.label || value}
            <button
              type="button"
              onClick={() => handleOptionRemove(value)}
              className="ml-2 hover:text-destructive transition-colors"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        );
      });
    } else if (condition.value) {
      // Single value
      const option = options.find(opt => opt.value === condition.value);
      return (
        <Badge variant="secondary" className="px-3 py-1 text-sm font-medium">
          {option?.label || condition.value}
        </Badge>
      );
    }
    
    return null;
  };

  const availableOperators = getAvailableOperators();
  const currentOperator = availableOperators.find(op => op.value === condition.operator);
  const questionOptions = getQuestionOptions();
  const selectedValues = Array.isArray(condition.value) ? condition.value : (condition.value ? [condition.value] : []);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
    >
      <Card className={cn(
        "border-2 transition-colors duration-200",
        condition.isValid ? "border-green-200 bg-green-50/30" : "border-red-200 bg-red-50/30"
      )}>
        <CardContent className="p-6">
          {/* Logic Operator Badge */}
          {showLogicOperator && (
            <div className="flex items-center gap-2 mb-4">
              <Badge variant="outline" className="px-3 py-1 text-sm font-medium">
                AND
              </Badge>
              <span className="text-sm text-muted-foreground">
                Both this and the previous condition must be true
              </span>
            </div>
          )}

          <div className="grid grid-cols-12 gap-4 items-start">
            {/* Question Selection */}
            <div className="col-span-12 sm:col-span-5 space-y-2">
              <Label className="text-sm font-medium">Question</Label>
              <Select 
                value={condition.question_id} 
                onValueChange={handleQuestionChange}
              >
                <SelectTrigger className="h-11 text-sm">
                  <SelectValue placeholder="Select a question..." />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {questions.map((question) => {
                    const questionId = question._id || question.id;
                    if (!questionId) return null;
                    
                    return (
                      <SelectItem key={questionId} value={questionId} className="py-3">
                        <div className="flex flex-col items-start gap-1">
                          <span className="font-medium text-sm leading-none">
                            {question.label}
                          </span>
                          <span className="text-xs text-muted-foreground capitalize">
                            {question.type.replace('_', ' ')}
                          </span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Operator Selection */}
            <div className="col-span-12 sm:col-span-2 space-y-2">
              <Label className="text-sm font-medium">Operator</Label>
              <Select
                value={condition.operator}
                onValueChange={handleOperatorChange}
                disabled={!selectedQuestion}
              >
                <SelectTrigger className="h-11 text-sm">
                  <SelectValue>
                    {currentOperator ? (
                      <span className="font-mono text-lg font-semibold">
                        {currentOperator.symbol}
                      </span>
                    ) : (
                      '='
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {availableOperators.map((operator) => (
                    <SelectItem key={operator.value} value={operator.value} className="py-2">
                      <div className="flex items-center gap-3">
                        <span className="font-mono text-sm font-semibold w-6">
                          {operator.symbol}
                        </span>
                        <span className="text-sm">{operator.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Value Selection */}
            <div className="col-span-12 sm:col-span-4 space-y-2">
              <Label className="text-sm font-medium">Value</Label>
              
              {!selectedQuestion ? (
                <div className="h-11 px-3 py-2 bg-muted rounded-md border flex items-center text-sm text-muted-foreground">
                  Select question first
                </div>
              ) : (
                <div className="space-y-3">
                  {/* Selected Values Display */}
                  {selectedValues.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {renderSelectedTags()}
                    </div>
                  )}

                  {/* Option Selection */}
                  <div className="border rounded-md p-3 bg-background min-h-[44px]">
                    {questionOptions.length === 0 ? (
                      <div className="text-sm text-muted-foreground">
                        No options available
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 gap-2">
                        {questionOptions.map((option) => (
                          <button
                            key={option.value}
                            type="button"
                            onClick={() => handleOptionToggle(option.value)}
                            className={cn(
                              "flex items-center gap-3 p-2 text-left text-sm rounded-md transition-colors",
                              "hover:bg-muted/50",
                              isOptionSelected(option.value) 
                                ? "bg-primary/10 border border-primary/20" 
                                : "bg-transparent border border-transparent"
                            )}
                          >
                            <div className={cn(
                              "w-4 h-4 rounded border-2 flex items-center justify-center transition-colors",
                              isOptionSelected(option.value)
                                ? "bg-primary border-primary text-primary-foreground"
                                : "border-muted-foreground/40"
                            )}>
                              {isOptionSelected(option.value) && (
                                <svg className="w-2.5 h-2.5" viewBox="0 0 12 12" fill="currentColor">
                                  <path d="M10 3L4.5 8.5L2 6" stroke="currentColor" strokeWidth="2" fill="none"/>
                                </svg>
                              )}
                            </div>
                            <span className="font-medium">{option.label}</span>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Helper Text */}
                  {selectedQuestion && selectedQuestion.type === 'multi_select' && 
                   (condition.operator === ExclusionOperator.IN || condition.operator === ExclusionOperator.NOT_IN) && (
                    <p className="text-xs text-muted-foreground">
                      Select multiple options. Submissions will be excluded if they match any selected option.
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Remove Button */}
            <div className="col-span-12 sm:col-span-1 flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={onRemove}
                className="h-11 w-11 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Error Message */}
          {condition.error && !condition.isValid && (
            <div className="mt-4 flex items-center gap-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              <span>{condition.error}</span>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
} 